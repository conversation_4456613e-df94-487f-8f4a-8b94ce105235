2025-08-12 17:15:57,215 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getMe "HTTP/1.1 200 OK"
2025-08-12 17:15:57,390 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 17:15:57,394 - telegram.ext.Application - INFO - Application started
2025-08-12 17:16:02,097 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:16:02,100 - __main__ - ERROR - 更新 None 导致错误 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:16:07,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:16:07,972 - __main__ - ERROR - 更新 None 导致错误 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:16:15,092 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:16:15,093 - __main__ - ERROR - 更新 None 导致错误 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:16:21,093 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:16:21,095 - __main__ - ERROR - 更新 None 导致错误 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:16:30,287 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:16:30,291 - __main__ - ERROR - 更新 None 导致错误 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:16:44,113 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:16:44,116 - __main__ - ERROR - 更新 None 导致错误 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:17:02,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:17:04,255 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:17:04,257 - __main__ - ERROR - 更新 None 导致错误 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:17:15,453 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:17:25,650 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:17:26,120 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:17:26,122 - __main__ - ERROR - 更新 None 导致错误 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
