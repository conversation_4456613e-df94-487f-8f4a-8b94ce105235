2025-08-12 18:05:32,400 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getMe "HTTP/1.1 200 OK"
2025-08-12 18:05:32,576 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 18:05:32,579 - telegram.ext.Application - INFO - Application started
2025-08-12 18:05:37,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 18:05:37,281 - __main__ - ERROR - 更新 None 导致错误 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 18:05:43,152 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 18:05:43,154 - __main__ - ERROR - 更新 None 导致错误 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 18:05:50,278 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 18:05:50,281 - __main__ - ERROR - 更新 None 导致错误 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 18:06:02,712 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:08,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:08,019 - __main__ - ERROR - API调用失败: HTTPConnectionPool(host='127.0.0.1', port=7893): Max retries exceeded with url: /register_merchant.php?merchant_id=7346442935 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fccc1e972b0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-12 18:06:08,736 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:06:10,137 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:10,142 - __main__ - ERROR - API调用失败: HTTPConnectionPool(host='127.0.0.1', port=7893): Max retries exceeded with url: /register_merchant.php?merchant_id=7346442935 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fccc1e8c8b0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-12 18:06:10,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:06:11,924 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:11,929 - __main__ - ERROR - API调用失败: HTTPConnectionPool(host='127.0.0.1', port=7893): Max retries exceeded with url: /register_merchant.php?merchant_id=7346442935 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fccc1e3d130>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-12 18:06:12,322 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:06:22,107 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:24,550 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:25,255 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:06:25,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:06:26,040 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:06:26,957 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:26,967 - __main__ - ERROR - API调用失败: HTTPConnectionPool(host='127.0.0.1', port=7893): Max retries exceeded with url: /get_merchant_info.php?merchant_id=7346442935 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fccc1e331c0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-08-12 18:06:27,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:06:37,136 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:47,306 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:57,490 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:07:07,667 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8171182904:AAGvgchJGoVO8X0eGzpl1e0lxfft7dGYA54/getUpdates "HTTP/1.1 200 OK"
