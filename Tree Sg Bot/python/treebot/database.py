import json
import datetime
import time
from typing import Dict, List, Optional
from config import DB_FILE, NEW_USER_FREE_TRIES

class Database:
    def __init__(self):
        self.data = self._load_data()

    def _load_data(self) -> Dict:
        try:
            with open(DB_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # 确保所有必要的键都存在
                if "broadcasts" not in data:
                    data["broadcasts"] = {}
                return data
        except FileNotFoundError:
            return {
                "users": {},
                "invites": [],
                "broadcasts": {}
            }

    def _save_data(self):
        with open(DB_FILE, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, ensure_ascii=False, indent=2)

    def get_user(self, user_id: int) -> Dict:
        if str(user_id) not in self.data["users"]:
            self.data["users"][str(user_id)] = {
                "tries": NEW_USER_FREE_TRIES,
                "vip_until": None,
                "last_sign_in": None,
                "invited_by": None,
                "invite_count": 0,
                "daily_invites": 0,
                "last_invite_reset": None
            }
            self._save_data()
        return self.data["users"][str(user_id)]

    def update_user(self, user_id: int, data: Dict):
        self.data["users"][str(user_id)].update(data)
        self._save_data()

    def add_invite(self, inviter_id: int, invited_id: int):
        invite_data = {
            "inviter_id": inviter_id,
            "invited_id": invited_id,
            "timestamp": datetime.datetime.now().isoformat()
        }
        self.data["invites"].append(invite_data)
        
        # 更新邀请人统计
        inviter = self.get_user(inviter_id)
        self.update_user(inviter_id, {
            "invite_count": inviter["invite_count"] + 1
        })
        
        # 检查邀请奖励
        total_invites = inviter["invite_count"] + 1
        if total_invites >= 20:
            self.add_vip_days(inviter_id, 15)
        elif total_invites >= 10:
            self.add_vip_days(inviter_id, 5)
        elif total_invites >= 5:
            self.add_vip_days(inviter_id, 2)
        
        self._save_data()

    def get_user_invites(self, user_id: int) -> List[Dict]:
        return [invite for invite in self.data["invites"] 
                if invite["inviter_id"] == user_id]

    def get_daily_invites(self, user_id: int) -> int:
        user = self.get_user(user_id)
        last_reset = user.get("last_invite_reset")
        today = datetime.datetime.now().date()
        
        if last_reset is None or datetime.datetime.fromisoformat(last_reset).date() < today:
            self.update_user(user_id, {"daily_invites": 0, "last_invite_reset": today.isoformat()})
            return 0
        return user["daily_invites"]

    def increment_daily_invites(self, user_id: int):
        user = self.get_user(user_id)
        daily_invites = self.get_daily_invites(user_id)
        self.update_user(user_id, {"daily_invites": daily_invites + 1})
        
        # 检查每日邀请奖励
        if daily_invites + 1 >= 3:
            self.add_vip_days(user_id, 1)

    def is_vip(self, user_id: int) -> bool:
        user = self.get_user(user_id)
        if not user["vip_until"]:
            return False
        return datetime.datetime.fromisoformat(user["vip_until"]) > datetime.datetime.now()

    def add_vip_days(self, user_id: int, days: int):
        user = self.get_user(user_id)
        now = datetime.datetime.now()
        if user["vip_until"] and datetime.datetime.fromisoformat(user["vip_until"]) > now:
            vip_until = datetime.datetime.fromisoformat(user["vip_until"])
        else:
            vip_until = now
        vip_until += datetime.timedelta(days=days)
        self.update_user(user_id, {"vip_until": vip_until.isoformat()})

    def get_all_users(self) -> List[int]:
        return [int(user_id) for user_id in self.data["users"].keys()]

    def add_broadcast(self, content: str) -> str:
        """添加广播消息并返回消息ID"""
        broadcast_id = str(int(time.time() * 1000))  # 使用时间戳作为ID
        if "broadcasts" not in self.data:
            self.data["broadcasts"] = {}
        self.data["broadcasts"][broadcast_id] = {
            "content": content,
            "created_at": datetime.datetime.now().isoformat()
        }
        self._save_data()
        return broadcast_id

    def get_broadcast(self, broadcast_id: str) -> Optional[str]:
        """获取广播消息内容"""
        broadcast = self.data["broadcasts"].get(broadcast_id)
        if broadcast:
            return broadcast.get("content")
        return None 