#!/bin/bash

# 目标目录
BOT_DIR="/python/treebot"
BOT_NAME="main.py"

# 日志目录
LOG_DIR="$BOT_DIR/logs"
mkdir -p "$LOG_DIR"

# 日志文件
DATE=$(date "+%Y-%m-%d")
CHECK_LOG="$LOG_DIR/check_$DATE.log"
BOT_LOG="$LOG_DIR/bot_$DATE.log"
ERROR_LOG="$LOG_DIR/error_$DATE.log"

# 日志轮转（保留最近7天的日志）
find "$LOG_DIR" -name "*.log" -mtime +7 -delete

cd "$BOT_DIR" || exit

# 获取当前时间
NOW=$(date "+%Y-%m-%d %H:%M:%S")

# 强制终止所有相关的Python进程
echo "[$NOW] INFO: Killing all existing bot processes..." >> "$CHECK_LOG"
pkill -f "python $BOT_DIR/$BOT_NAME"
sleep 2  # 等待进程完全终止

# 检查是否运行中（只匹配该路径的 main.py）
if ! pgrep -f "python $BOT_DIR/$BOT_NAME" > /dev/null; then
    echo "[$NOW] INFO: Bot not running. Starting $BOT_NAME..." >> "$CHECK_LOG"
    echo "[$NOW] INFO: Starting bot process..." >> "$BOT_LOG"
    
    # 启动机器人并将输出重定向到日志文件
    nohup python "$BOT_DIR/$BOT_NAME" >> "$BOT_LOG" 2>> "$ERROR_LOG" &
    
    # 记录进程ID
    BOT_PID=$!
    echo "[$NOW] INFO: Bot started with PID: $BOT_PID" >> "$CHECK_LOG"
    
    # 等待几秒检查进程是否成功启动
    sleep 5
    if ps -p $BOT_PID > /dev/null; then
        echo "[$NOW] INFO: Bot successfully started and running" >> "$CHECK_LOG"
    else
        echo "[$NOW] ERROR: Bot failed to start properly" >> "$CHECK_LOG"
        echo "[$NOW] ERROR: Check error log for details" >> "$CHECK_LOG"
    fi
else
    BOT_PID=$(pgrep -f "python $BOT_DIR/$BOT_NAME")
    echo "[$NOW] INFO: Bot is already running with PID: $BOT_PID" >> "$CHECK_LOG"
    
    # 检查进程状态
    if ps -p $BOT_PID > /dev/null; then
        echo "[$NOW] INFO: Bot process is healthy" >> "$CHECK_LOG"
    else
        echo "[$NOW] WARNING: Bot process might be in an inconsistent state" >> "$CHECK_LOG"
    fi
fi

# 记录系统资源使用情况
echo "[$NOW] INFO: System resource usage:" >> "$CHECK_LOG"
echo "Memory usage:" >> "$CHECK_LOG"
free -h | grep -v + >> "$CHECK_LOG"
echo "Disk usage:" >> "$CHECK_LOG"
df -h "$BOT_DIR" >> "$CHECK_LOG"
