Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 16, in <module>
    from config import *
  File "/python/Tree Sg Bot/config.py", line 2, in <module>
    from dotenv import load_dotenv
ModuleNotFoundError: No module named 'dotenv'
2025-08-12 17:57:06,673 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 17:57:06,846 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 17:57:06,849 - telegram.ext.Application - INFO - Application started
2025-08-12 17:57:29,171 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:57:29,175 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 670, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:57:34,521 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:57:34,524 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 670, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:57:37,855 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:57:38,774 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:57:40,869 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:57:40,870 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 670, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:57:44,255 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:57:44,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:57:44,982 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:57:45,362 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:57:47,474 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:57:47,478 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 670, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:57:56,516 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:57:57,228 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:57:59,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:57:59,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:58:07,517 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:58:07,519 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 17:58:07,520 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 17:58:10,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 17:58:10,736 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 17:58:10,738 - telegram.ext.Application - INFO - Application started
2025-08-12 17:58:15,704 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:58:16,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:58:16,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:58:17,278 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 17:58:17,644 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 17:58:18,056 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 17:58:47,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:59:17,303 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:59:47,487 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:00:02,061 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:00:02,063 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 18:00:02,064 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 18:00:04,578 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 18:00:04,747 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 18:00:04,750 - telegram.ext.Application - INFO - Application started
2025-08-12 18:00:35,274 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:05,463 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:12,155 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:12,854 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:01:13,217 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:01:16,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:19,674 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:20,648 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:01:22,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:52,638 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:02:22,821 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:02:53,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:03:23,185 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:03:53,377 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:04:23,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:04:53,764 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:04:54,217 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:04:55,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:05:03,826 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:05:04,536 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:05:04,884 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 18:05:05,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:05:34,012 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:04,201 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:34,391 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:07:04,582 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:07:34,763 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:04,941 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:17,523 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:18,406 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:08:20,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:20,370 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:20,747 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 18:08:21,232 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:22,273 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:22,726 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:23,097 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:24,322 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:24,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:24,866 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 18:08:25,236 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:31,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:32,378 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:08:33,129 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:33,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:33,862 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:35,144 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:35,490 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:35,866 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:36,564 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:36,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:37,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 18:08:37,709 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:38,723 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:39,080 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:39,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:09:08,901 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:09:39,085 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:10:02,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:10:02,286 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 18:10:02,287 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 18:10:04,811 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 18:10:04,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 18:10:04,994 - telegram.ext.Application - INFO - Application started
2025-08-12 18:10:35,520 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:11:02,116 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:11:32,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:12:02,477 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:12:32,661 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:12:43,040 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:12:43,766 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:12:44,131 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:13:13,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:13:43,416 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
